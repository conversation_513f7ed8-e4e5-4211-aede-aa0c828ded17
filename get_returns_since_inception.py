import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>
from sqlalchemy import create_engine
import os
import boto3
import json
from botocore.exceptions import ClientError
import numpy as np
from io import BytesIO, StringIO


def get_secret(secret_name):
    region_name = "ap-southeast-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        raise e

    secret = get_secret_value_response['SecretString']
    return json.loads(secret)


def detect_invocation_source(event):
    try:
        if isinstance(event, dict):
            if 'httpMethod' in event and 'body' in event:
                body = json.loads(event['body']) if isinstance(event['body'], str) else {}
                return 'api_gateway_rest', body
            elif 'detail-type' in event and event['detail-type'] == 'Scheduled Event':
                return 'eventbridge', event.get('detail', {})
            else:
                return 'aws_sdk', event
    except Exception as e:
        print(f"[detect_invocation_source] Error detecting source or parsing body: {e}")

    return 'unknown', {}


def get_scheduler_variables(context):
    function_name = context.function_name
    # print(f"Fetching scheduler variables for: {function_name}")

    try:
        db_config = {
            'host': '************',
            'user': 'edl_remote_1',
            'password': 'A6qj474yY687',
            'port': 3306,
            'database': 'sunrise_iat'
        }
        
        # parts = function_name.split('_')
        # if len(parts) >= 2:
        #     env = parts[1]  # This gets 'stg2' or 'prod' from the function name
            
        #     # Set secret name based on environment
        #     if env == 'stg2':
        #         secret_name = 'sr-stg-database-env-secret-01'
        #     elif env == 'prod':
        #         secret_name = 'sr-prod-database-env-secret-01'
        #     else:
        #         # Default fallback or raise an error
        #         secret_name = 'sr-stg-database-env-secret-01'  # default to staging
        # else:
        #     # Fallback if function name doesn't match expected format
        #     secret_name = 'sr-stg-database-env-secret-01'
        
        # db_secret = get_secret(secret_name)
        # db_config = {
        #     'host': db_secret['host'],
        #     'user': db_secret['username'],
        #     'password': db_secret['password'],
        #     'port': db_secret.get('port', 3306),
        #     'database': 'sunrise_wp'
        # }

        connection = mysql.connector.connect(**db_config)

        if not connection.is_connected():
            # print("Failed to connect to MySQL")
            return

        cursor = connection.cursor(dictionary=True)

        query = """
            SELECT variables
            FROM lambda_scheduler_variables
            WHERE function_name = %s
            LIMIT 1
        """
        cursor.execute(query, (function_name,))
        row = cursor.fetchone()

        if row and row['variables']:
            return json.loads(row['variables'])

    except Error as e:
        print(f"[get_scheduler_variables] MySQL Error: {str(e)}")
        raise

    except Exception as e:
        print(f"[get_scheduler_variables] Error in get_scheduler_variables: {e}")
        raise

    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None and connection.is_connected():
            connection.close()
            print("[get_scheduler_variables] MySQL connection closed.")

    return {}


def write_csv_to_s3(df, bucket_name, s3_key):
    """Write DataFrame to CSV file in S3 using pandas"""
    try:
        s3_client = boto3.client('s3')

        # Create CSV in memory using pandas
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_content = csv_buffer.getvalue()

        # Upload to S3
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=csv_content.encode('utf-8'),
            ContentType='text/csv'
        )

        # print(f"CSV file successfully uploaded to s3://{bucket_name}/{s3_key}")
        return True
    except Exception as e:
        print(f"Error writing CSV file to S3: {str(e)}")
        raise


def write_xlsx_to_s3(df, bucket_name, s3_key):
    """Write DataFrame to Excel file in S3 using pandas"""
    try:
        s3_client = boto3.client('s3')

        # Create Excel file in memory using pandas
        excel_buffer = BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Fund_Returns')

        excel_buffer.seek(0)

        # Upload to S3
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=excel_buffer.getvalue(),
            ContentType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # print(f"Excel file successfully uploaded to s3://{bucket_name}/{s3_key}")
        return True
    except Exception as e:
        print(f"Error writing Excel file to S3: {str(e)}")
        raise


def get_returns_since_inception(db_config, tbl, fund_name, end_date):
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**db_config)
        if not connection.is_connected():
            return None

        db = db_config['database']
        tablename = tbl

        cursor = connection.cursor()

        # Handle both single fund_name and list of fund_names
        if isinstance(fund_name, (list, tuple)):
            placeholders = ', '.join(['%s'] * len(fund_name))
            query = f"""
                SELECT date, net_monthly_returns, fund_name
                FROM {db}.{tablename}
                WHERE fund_name IN ({placeholders}) AND date <= %s
                ORDER BY date
            """
            params = list(fund_name) + [end_date]
        else:
            query = f"""
                SELECT date, net_monthly_returns, fund_name
                FROM {db}.{tablename}
                WHERE fund_name = %s AND date <= %s
                ORDER BY date
            """
            params = [fund_name, end_date]

        cursor.execute(query, params)
        rows = cursor.fetchall()

        df = pd.DataFrame(rows, columns=['date', 'net_monthly_returns', 'fund_name'])
        df = df.dropna(subset=['net_monthly_returns'])
        df = df.sort_values(by=['date'])

        if df.empty:
            print(f"[get_returns_since_inception] No data found for {fund_name} up to {end_date}")
            return None

        return df

    except Error as e:
        raise
    except Exception as e:
        raise
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()


def lambda_handler(event, context):
    
    try:
        source, body = detect_invocation_source(event)
        if source == 'eventbridge':
            body = get_scheduler_variables(context)

        configs_to_run = body.get('function_configs')
        
        if not configs_to_run:
            configs_to_run = [{}]


        results = []

        

        for i, config in enumerate(configs_to_run):
            # print(f"\n--- Processing configuration {i+1}/{len(configs_to_run)} ---")

            # Extract configuration parameters - use dynamic lambda name from context
            database = config.get('database', 'iat_inv_1')
            table_name = config.get('table_name', 'benchmark_returns')
            s3_bucket = config.get('s3_bucket', 'edl-test-lambda-bucket-04242025')
            lambda_name = context.function_name 
            xlsx_filename = config.get('input_filename', 'input_benchmark_monthly_returns_1.xlsx')
            generate_output = str(config.get('generate_output', 'false')).lower() == 'true'
            output_format = config.get('output_format', 'xlsx')
            batch_size = config.get('batch_size', 1000)

            if not all([database, s3_bucket]):
                raise ValueError(f'Configuration {i+1} failed: Missing required parameters (database, s3_bucket)')

            # Construct S3 paths using dynamic lambda name
            input_s3_key = f"lambda-function/{lambda_name}/input/{xlsx_filename}"
            
            # Determine output file extension and key
            output_extension = 'csv' if output_format.lower() == 'csv' else 'xlsx'
            output_filename = f"processed_{xlsx_filename.rsplit('.', 1)[0]}.{output_extension}"
            output_s3_key = f"lambda-function/{lambda_name}/output/{output_filename}"

            # print(f"Reading Excel file from: s3://{s3_bucket}/{input_s3_key}")

            # Read Excel file from S3
            df = read_xlsx_from_s3(s3_bucket, input_s3_key)

            # Get database configuration
            db_secret = get_secret('prod-sr-aws-rds-mysql1-sol2')
            db_config = {
                'host': db_secret['host'],
                'user': db_secret['username'],
                'password': db_secret['password'],
                'port': db_secret.get('port', 3306),
                'database': database
            }

            total_processed, processed_df = process_populate_benchmark_returns(df, db_config, table_name, batch_size)

            # Generate output file if requested
            output_location = None
            if generate_output and not processed_df.empty:
                # print(f"Generating output {output_format.upper()} file...")
                
                if output_format.lower() == 'csv':
                    write_csv_to_s3(processed_df, s3_bucket, output_s3_key)
                else:
                    write_xlsx_to_s3(processed_df, s3_bucket, output_s3_key)
                    
                output_location = f"s3://{s3_bucket}/{output_s3_key}"
                # print(f"Output file saved to: {output_location}")

            results.append({
                'config_index': i+1,
                'table': table_name,
                'status': 'success',
                'records_processed': total_processed,
                'input_file': f"{xlsx_filename}",
            })

            # print(f"Configuration {i+1} completed successfully")

        successful = len(results)

        print(f"\n--- Summary ---")
        print(f"Total configurations processed: {successful}")
        print(f"Results: {results}")

        return {
            'statusCode': 200,
            'body': 'Process completed successfully.'
        }

    except Error as e:
        print(f"MySQL Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': f'Database error: {str(e)}'
        }

    except Exception as ex:
        print(f"Unhandled exception: {str(ex)}")
        return {
            'statusCode': 500,
            'body': f'Unhandled error: {str(ex)}'
        }